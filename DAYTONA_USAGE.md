# Daytona Sandbox 使用指南

本项目集成了 Daytona TypeScript SDK，提供了创建 Sandbox 和文件同步功能。

## 功能概述

### 1. 创建 Sandbox
- 使用 Daytona API 创建新的开发环境
- 支持自定义环境变量和配置
- 自动管理 Sandbox 生命周期

### 2. 文件同步
- 批量上传本地文件到 Sandbox
- 支持多种文件类型
- 实时显示上传进度和结果

### 3. 安全管理
- API 密钥通过环境变量管理
- 服务器端处理所有 Daytona API 调用
- 客户端不直接暴露敏感信息

## 环境配置

### 必需的环境变量

在 `.env.local` 文件中设置以下变量：

```env
# Daytona API 密钥
DAYTONA_API_KEY=your_api_key_here

# Daytona API 端点（可选，默认为官方 API）
DAYTONA_API_URL=https://app.daytona.io/api

# 目标位置（必需）
DAYTONA_TARGET=us
```

### 获取 API 密钥

1. 访问 [Daytona 控制台](https://app.daytona.io)
2. 登录你的账户
3. 导航到 API 设置页面
4. 生成新的 API 密钥
5. 选择合适的目标位置（如：us, eu, asia）

## 使用步骤

### 1. 启动应用

```bash
npm run dev
```

### 2. 访问界面

打开浏览器访问 `http://localhost:3000`

### 3. 创建 Sandbox

1. 点击 "🚀 Daytona Sandbox" 按钮展开管理界面
2. 点击 "创建 Sandbox" 按钮
3. 等待 Sandbox 创建完成（通常需要 1-2 分钟）
4. 查看操作日志了解创建状态

### 4. 上传文件

1. 点击 "选择文件" 按钮选择要上传的文件
2. 可以选择多个文件（支持 Ctrl/Cmd + 多选）
3. 查看已选择的文件列表
4. 点击 "上传文件" 开始同步
5. 文件将被上传到 Sandbox 的 `/tmp` 目录

### 5. 验证上传

上传完成后，日志会显示：
- 上传的文件数量
- 每个文件的名称和大小
- 上传状态和结果

## API 端点

### POST /api/daytona

支持以下操作：

#### 创建 Sandbox
```json
{
  "action": "create",
  "language": "typescript",
  "envVars": {
    "NODE_ENV": "development"
  },
  "autoStopInterval": 60,
  "autoArchiveInterval": 10080,
  "timeout": 120
}
```

#### 上传文件
```json
{
  "action": "uploadFiles",
  "sandboxId": "sandbox_id",
  "files": [
    {
      "name": "hello.js",
      "content": "console.log('Hello World!');",
      "destination": "/tmp/hello.js"
    }
  ],
  "timeout": 300
}
```

#### 获取 Sandbox 信息
```json
{
  "action": "get",
  "sandboxId": "sandbox_id"
}
```

#### 列出文件
```json
{
  "action": "listFiles",
  "sandboxId": "sandbox_id",
  "path": "/tmp"
}
```

### GET /api/daytona

- 不带参数：列出所有 Sandbox
- 带 `sandboxId` 参数：获取特定 Sandbox 信息

## 测试文件

项目包含了一些测试文件在 `test-files/` 目录：

- `hello.js` - JavaScript 示例文件
- `config.json` - JSON 配置文件
- `README.md` - Markdown 文档

你可以使用这些文件来测试上传功能。

## 故障排除

### 常见问题

1. **创建 Sandbox 失败**
   - 检查环境变量是否正确设置
   - 验证 API 密钥是否有效
   - 确认目标位置是否正确

2. **文件上传失败**
   - 确保 Sandbox 已成功创建
   - 检查文件大小是否过大
   - 验证文件内容是否为文本格式

3. **API 调用超时**
   - 检查网络连接
   - 增加超时时间设置
   - 重试操作

### 调试技巧

1. 查看浏览器开发者工具的网络标签
2. 检查服务器端日志
3. 使用操作日志面板查看详细信息

## 扩展功能

你可以基于现有代码扩展以下功能：

1. **文件下载** - 从 Sandbox 下载文件到本地
2. **目录管理** - 创建、删除目录
3. **文件编辑** - 在线编辑 Sandbox 中的文件
4. **终端访问** - 在 Sandbox 中执行命令
5. **实时同步** - 监听本地文件变化并自动同步

## 安全注意事项

1. 永远不要在客户端代码中硬编码 API 密钥
2. 使用环境变量管理敏感配置
3. 定期轮换 API 密钥
4. 限制 Sandbox 的自动停止和删除时间
5. 监控 API 使用量和成本

## 支持

如果遇到问题，可以：

1. 查看 [Daytona 官方文档](https://www.daytona.io/docs)
2. 访问 [Daytona TypeScript SDK 文档](https://www.daytona.io/docs/typescript-sdk)
3. 在项目 GitHub 仓库提交 Issue
