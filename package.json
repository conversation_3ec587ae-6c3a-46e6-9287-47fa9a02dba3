{"name": "my-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts"}, "dependencies": {"@opennextjs/cloudflare": "^1.6.2", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "@daytonaio/sdk": "^0.25.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5", "wrangler": "^4.26.1"}}