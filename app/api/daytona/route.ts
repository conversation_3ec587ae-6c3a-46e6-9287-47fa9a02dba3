import { NextRequest, NextResponse } from 'next/server';
import { Daytona } from '@daytonaio/sdk';

interface DaytonaRequestBody {
  action: string;
  sandboxId?: string;
  language?: string;
  envVars?: Record<string, string>;
  autoStopInterval?: number;
  autoArchiveInterval?: number;
  timeout?: number;
  files?: Array<{
    name: string;
    content: string;
    destination?: string;
  }>;
  path?: string;
  labels?: Record<string, string>;
}

// 从环境变量获取配置
const getDaytonaConfig = () => {
  const apiKey = process.env.DAYTONA_API_KEY;
  const apiUrl = process.env.DAYTONA_API_URL || 'https://app.daytona.io/api';
  const target = process.env.DAYTONA_TARGET;

  if (!apiKey || !target) {
    throw new Error('Missing required environment variables: DAYTONA_API_KEY and DAYTONA_TARGET');
  }

  return { apiKey, apiUrl, target };
};

// 创建 Sandbox
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as DaytonaRequestBody;
    const { action, ...params } = body;

    const config = getDaytonaConfig();
    const daytona = new Daytona(config);

    switch (action) {
      case 'create': {
        const sandbox = await daytona.create({
          language: params.language || 'typescript',
          envVars: params.envVars || { NODE_ENV: 'development' },
          autoStopInterval: params.autoStopInterval || 60,
          autoArchiveInterval: params.autoArchiveInterval || 60 * 24 * 7,
        }, { timeout: params.timeout || 120 });

        return NextResponse.json({
          success: true,
          data: {
            id: sandbox.id,
            state: sandbox.state,
            createdAt: new Date().toISOString()
          }
        });
      }

      case 'get': {
        if (!params.sandboxId) {
          return NextResponse.json(
            { success: false, error: 'Missing sandboxId' },
            { status: 400 }
          );
        }

        const sandbox = await daytona.get(params.sandboxId);
        return NextResponse.json({
          success: true,
          data: {
            id: sandbox.id,
            state: sandbox.state
          }
        });
      }

      case 'list': {
        const sandboxes = await daytona.list(params.labels);
        return NextResponse.json({
          success: true,
          data: sandboxes.map(sandbox => ({
            id: sandbox.id,
            state: sandbox.state
          }))
        });
      }

      case 'delete': {
        if (!params.sandboxId) {
          return NextResponse.json(
            { success: false, error: 'Missing sandboxId' },
            { status: 400 }
          );
        }

        const sandbox = await daytona.get(params.sandboxId);
        await daytona.delete(sandbox, params.timeout || 60);
        
        return NextResponse.json({
          success: true,
          message: 'Sandbox deleted successfully'
        });
      }

      case 'uploadFiles': {
        if (!params.sandboxId || !params.files) {
          return NextResponse.json(
            { success: false, error: 'Missing sandboxId or files' },
            { status: 400 }
          );
        }

        const sandbox = await daytona.get(params.sandboxId);
        
        // 准备文件上传数据
        const fileUploads = params.files.map((file) => ({
          source: Buffer.from(file.content, 'utf-8'),
          destination: file.destination || `/tmp/${file.name}`
        }));

        // 批量上传文件
        await sandbox.fs.uploadFiles(fileUploads, params.timeout || 300);

        // 列出上传后的文件以确认
        const uploadedFiles = await sandbox.fs.listFiles('/tmp');
        const uploadedFileNames = uploadedFiles
          .filter(file => params.files?.some((f) => f.name === file.name))
          .map(file => ({ name: file.name, size: file.size }));

        return NextResponse.json({
          success: true,
          data: {
            uploadedCount: fileUploads.length,
            uploadedFiles: uploadedFileNames
          }
        });
      }

      case 'listFiles': {
        if (!params.sandboxId) {
          return NextResponse.json(
            { success: false, error: 'Missing sandboxId' },
            { status: 400 }
          );
        }

        const sandbox = await daytona.get(params.sandboxId);
        const files = await sandbox.fs.listFiles(params.path || '/tmp');
        
        return NextResponse.json({
          success: true,
          data: files.map(file => ({
            name: file.name,
            size: file.size,
            isDir: file.isDir,
            modTime: file.modTime
          }))
        });
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Daytona API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    );
  }
}

// 获取 Sandbox 信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sandboxId = searchParams.get('sandboxId');

    if (!sandboxId) {
      // 列出所有 Sandbox
      const config = getDaytonaConfig();
      const daytona = new Daytona(config);
      const sandboxes = await daytona.list();
      
      return NextResponse.json({
        success: true,
        data: sandboxes.map(sandbox => ({
          id: sandbox.id,
          state: sandbox.state
        }))
      });
    } else {
      // 获取特定 Sandbox
      const config = getDaytonaConfig();
      const daytona = new Daytona(config);
      const sandbox = await daytona.get(sandboxId);
      
      return NextResponse.json({
        success: true,
        data: {
          id: sandbox.id,
          state: sandbox.state
        }
      });
    }

  } catch (error) {
    console.error('Daytona API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    );
  }
}
