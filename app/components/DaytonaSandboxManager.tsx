'use client';

import { useState, useRef, useId } from 'react';

interface SandboxInfo {
  id: string;
  state: string;
  createdAt: string;
}

interface FileUploadInfo {
  name: string;
  size: number;
  content: string;
}

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export function DaytonaSandboxManager() {
  const [sandbox, setSandbox] = useState<SandboxInfo | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [files, setFiles] = useState<FileUploadInfo[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const fileInputId = useId();

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const createSandbox = async () => {
    setIsCreating(true);
    try {
      addLog('正在创建 Sandbox...');

      const response = await fetch('/api/daytona', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create',
          language: 'typescript',
          envVars: {
            NODE_ENV: 'development'
          },
          autoStopInterval: 60, // 60分钟后自动停止
          autoArchiveInterval: 60 * 24 * 7, // 7天后自动归档
          timeout: 120 // 2分钟超时
        }),
      });

      const result = await response.json() as ApiResponse;

      if (result.success) {
        setSandbox(result.data);
        addLog(`Sandbox 创建成功! ID: ${result.data.id}`);
        addLog(`状态: ${result.data.state}`);
      } else {
        addLog(`创建 Sandbox 失败: ${result.error}`);
      }

    } catch (error) {
      addLog(`创建 Sandbox 失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsCreating(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles) return;

    const filePromises = Array.from(selectedFiles).map(file => {
      return new Promise<FileUploadInfo>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          resolve({
            name: file.name,
            size: file.size,
            content: e.target?.result as string
          });
        };
        reader.onerror = reject;
        reader.readAsText(file);
      });
    });

    Promise.all(filePromises)
      .then(fileInfos => {
        setFiles(prev => [...prev, ...fileInfos]);
        addLog(`已选择 ${fileInfos.length} 个文件`);
      })
      .catch(error => {
        addLog(`读取文件失败: ${error.message}`);
      });
  };

  const uploadFiles = async () => {
    if (!sandbox || files.length === 0) {
      addLog('请先创建 Sandbox 并选择文件');
      return;
    }

    setIsUploading(true);
    try {
      addLog('正在上传文件到 Sandbox...');

      const response = await fetch('/api/daytona', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'uploadFiles',
          sandboxId: sandbox.id,
          files: files.map(file => ({
            name: file.name,
            content: file.content,
            destination: `/tmp/${file.name}`
          })),
          timeout: 300 // 5分钟超时
        }),
      });

      const result = await response.json() as ApiResponse;

      if (result.success) {
        addLog(`成功上传 ${result.data.uploadedCount} 个文件到 Sandbox`);

        if (result.data.uploadedFiles.length > 0) {
          addLog('已上传的文件:');
          result.data.uploadedFiles.forEach((file: { name: string; size: number }) =>
            addLog(`  - ${file.name} (${file.size} bytes)`)
          );
        }
      } else {
        addLog(`上传文件失败: ${result.error}`);
      }

    } catch (error) {
      addLog(`上传文件失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsUploading(false);
    }
  };

  const clearFiles = () => {
    setFiles([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    addLog('已清空文件列表');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
        Daytona Sandbox 管理器
      </h2>

      {/* 说明区域 */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
        <p className="text-blue-800 dark:text-blue-200 text-sm">
          <strong>注意:</strong> 请确保在环境变量中设置了 <code>DAYTONA_API_KEY</code> 和 <code>DAYTONA_TARGET</code>。
          API 配置通过服务器端环境变量管理，确保安全性。
        </p>
      </div>

      {/* Sandbox 状态 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">Sandbox 状态</h3>
        {sandbox ? (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
            <p className="text-green-800 dark:text-green-200">
              <strong>ID:</strong> {sandbox.id}
            </p>
            <p className="text-green-800 dark:text-green-200">
              <strong>状态:</strong> {sandbox.state}
            </p>
            <p className="text-green-800 dark:text-green-200">
              <strong>创建时间:</strong> {new Date(sandbox.createdAt).toLocaleString()}
            </p>
          </div>
        ) : (
          <div className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md p-4">
            <p className="text-gray-600 dark:text-gray-400">尚未创建 Sandbox</p>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-4">
        <button
          type="button"
          onClick={createSandbox}
          disabled={isCreating}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          {isCreating ? '创建中...' : '创建 Sandbox'}
        </button>

        <div className="flex items-center gap-2">
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={handleFileSelect}
            className="hidden"
            id={fileInputId}
          />
          <label
            htmlFor={fileInputId}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 cursor-pointer"
          >
            选择文件
          </label>

          <button
            type="button"
            onClick={uploadFiles}
            disabled={isUploading || !sandbox || files.length === 0}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isUploading ? '上传中...' : '上传文件'}
          </button>

          <button
            type="button"
            onClick={clearFiles}
            disabled={files.length === 0}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            清空文件
          </button>
        </div>
      </div>

      {/* 文件列表 */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">
            已选择的文件 ({files.length})
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-4 max-h-40 overflow-y-auto">
            {files.map((file) => (
              <div key={`${file.name}-${file.size}`} className="flex justify-between items-center py-1">
                <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {(file.size / 1024).toFixed(1)} KB
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 日志区域 */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">操作日志</h3>
          <button
            type="button"
            onClick={clearLogs}
            className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清空日志
          </button>
        </div>
        <div className="bg-black text-green-400 rounded-md p-4 h-64 overflow-y-auto font-mono text-sm">
          {logs.length === 0 ? (
            <p className="text-gray-500">暂无日志...</p>
          ) : (
            logs.map((log, logIndex) => (
              <div key={`log-${logIndex}-${log.substring(0, 20)}`} className="mb-1">
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
