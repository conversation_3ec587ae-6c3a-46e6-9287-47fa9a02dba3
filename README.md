# My Next.js App with Daytona Integration

这是一个集成了 Daytona Sandbox 功能的 Next.js 项目，允许你创建 Sandbox 并同步文件到其中。

## 功能特性

- 🚀 创建 Daytona Sandbox
- 📁 批量上传文件到 Sandbox
- 📊 实时操作日志
- 🔒 安全的 API 密钥管理（服务器端）
- 🎨 现代化的 UI 界面

## 环境配置

1. 复制环境变量示例文件：
```bash
cp .env.local.example .env.local
```

2. 编辑 `.env.local` 文件，填入你的 Daytona 配置：
```env
DAYTONA_API_KEY=your_daytona_api_key_here
DAYTONA_API_URL=https://app.daytona.io/api
DAYTONA_TARGET=your_target_location_here
```

### 获取 Daytona API Key

1. 访问 [Daytona 控制台](https://app.daytona.io)
2. 登录你的账户
3. 在设置中生成 API Key
4. 选择合适的 Target 位置（如：us, eu, asia）

## 开始使用

首先，安装依赖：

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

然后，启动开发服务器：

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 使用 Daytona 功能

1. 点击页面上的 "🚀 Daytona Sandbox" 按钮
2. 点击 "创建 Sandbox" 来创建一个新的 Sandbox
3. 使用 "选择文件" 按钮选择要上传的文件
4. 点击 "上传文件" 将文件同步到 Sandbox
5. 查看操作日志了解详细信息

## API 路由

项目包含以下 API 端点：

- `POST /api/daytona` - 创建 Sandbox、上传文件等操作
- `GET /api/daytona` - 获取 Sandbox 信息

## 技术栈

- [Next.js 15](https://nextjs.org) - React 框架
- [Daytona TypeScript SDK](https://www.daytona.io/docs/typescript-sdk) - Sandbox 管理
- [Tailwind CSS](https://tailwindcss.com) - 样式框架
- [TypeScript](https://www.typescriptlang.org) - 类型安全

## 部署

### Vercel 部署

最简单的部署方式是使用 [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme)：

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 在 Vercel 项目设置中添加环境变量：
   - `DAYTONA_API_KEY`
   - `DAYTONA_API_URL`
   - `DAYTONA_TARGET`

### Cloudflare Pages 部署

项目已配置支持 Cloudflare Pages：

```bash
npm run deploy
```

确保在 Cloudflare Pages 设置中添加相同的环境变量。

## 了解更多

- [Next.js 文档](https://nextjs.org/docs) - 学习 Next.js 功能和 API
- [Daytona 文档](https://www.daytona.io/docs) - 学习 Daytona Sandbox 功能
- [Daytona TypeScript SDK](https://www.daytona.io/docs/typescript-sdk) - SDK 参考文档

## 贡献

欢迎提交 Issue 和 Pull Request！
